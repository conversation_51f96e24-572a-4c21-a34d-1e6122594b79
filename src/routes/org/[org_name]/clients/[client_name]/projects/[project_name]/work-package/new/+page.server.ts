import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { superValidate, message } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { workPackageSchema } from '$lib/schemas/work_package';
import { purchaseOrderModalSchema } from '$lib/schemas/purchase_order';
import { vendorSchema } from '$lib/schemas/vendor';
import { redirect } from 'sveltekit-flash-message/server';
import { requireUser, requireProject } from '$lib/server/auth';
import { error } from '@sveltejs/kit';
import { createVendor, createVendorModal } from '$lib/components/forms/vendor/vendor_form_actions';
import { createPurchaseOrderModal } from '$lib/components/forms/purchase_order/purchase_order_form_actions';

export const load: PageServerLoad = async ({ locals }) => {
	await requireUser();
	const { supabase } = locals;
	const { org_name, client_name, project_name } = requireProject();

	// Get project information
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select('*, client!inner(name, organization(name, org_id))')
		.eq('client.organization.name', org_name)
		.eq('client.name', client_name)
		.eq('name', project_name)
		.limit(1)
		.maybeSingle();

	if (projectError || !projectData) {
		console.error('Error fetching project:', projectError);
		throw error(404, 'Project not found');
	}

	// Fetch purchase orders for selection
	const { data: purchaseOrders, error: purchaseOrdersError } = await supabase.rpc(
		'get_purchase_orders_for_work_package',
		{
			project_id_param: projectData.project_id,
		},
	);

	if (purchaseOrdersError) {
		console.error('Error fetching purchase orders:', purchaseOrdersError);
		throw error(500, 'Failed to fetch purchase orders');
	}

	// Fetch WBS library items for selection
	const { data: wbsItems, error: wbsItemsError } = await supabase.rpc(
		'get_wbs_items_for_work_package',
		{
			project_id_param: projectData.project_id,
		},
	);

	if (wbsItemsError) {
		console.error('Error fetching WBS items:', wbsItemsError);
		throw error(500, 'Failed to fetch WBS items');
	}

	// Fetch accessible vendors for this project
	const { data: vendors, error: vendorsError } = await supabase.rpc('get_accessible_vendors', {
		user_id_param: (await requireUser()).user.id,
		entity_type_param: 'project',
		entity_id_param: projectData.project_id,
	});

	if (vendorsError) {
		console.error('Error fetching vendors:', vendorsError);
		throw error(500, 'Failed to fetch vendors');
	}

	const form = await superValidate(zod(workPackageSchema));
	const newPurchaseOrderForm = await superValidate(zod(purchaseOrderModalSchema));
	const newVendorForm = await superValidate(zod(vendorSchema));

	return {
		form,
		newPurchaseOrderForm,
		newVendorForm,
		project: projectData,
		purchaseOrders: purchaseOrders || [],
		wbsItems: wbsItems || [],
		vendors: vendors || [],
	};
};

export const actions: Actions = {
	createVendor,
	createVendorModal,
	createPurchaseOrderModal: async ({ request, locals }) => {
		const { user } = await requireUser();
		const { supabase } = locals;
		const { org_name, client_name, project_name } = requireProject();

		// Get project_id
		const { data: projectData, error: projectError } = await supabase
			.from('project')
			.select('*, client!inner(name, organization(name, org_id))')
			.eq('client.organization.name', org_name)
			.eq('client.name', client_name)
			.eq('name', project_name)
			.limit(1)
			.maybeSingle();

		if (projectError || !projectData) {
			console.error('Error fetching project:', projectError);
			return fail(404, { message: { type: 'error', text: 'Project not found' } });
		}

		const form = await superValidate(request, zod(purchaseOrderModalSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Set the project_id in the form data
		form.data.project_id = projectData.project_id;

		// Prepare the purchase order data
		const purchaseOrderData = {
			...form.data,
			created_by_user_id: user.id,
		};

		// Insert the purchase order and get vendor name
		const { data: purchaseOrder, error: purchaseOrderError } = await supabase
			.from('purchase_order')
			.insert(purchaseOrderData)
			.select(
				`
				purchase_order_id,
				po_number,
				description,
				po_date,
				vendor_id,
				account,
				original_amount,
				co_amount,
				freight,
				tax,
				other,
				notes,
				vendor:vendor_id (name)
			`,
			)
			.single();

		if (purchaseOrderError) {
			console.error('Error creating purchase order:', purchaseOrderError);
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to create purchase order' },
			});
		}

		// Return success with purchase order data instead of redirecting
		return {
			form: message(form, {
				type: 'success',
				text: `Purchase order "${purchaseOrder.po_number}" created successfully`,
			}),
			purchaseOrder: {
				purchase_order_id: purchaseOrder.purchase_order_id,
				po_number: purchaseOrder.po_number,
				description: purchaseOrder.description,
				po_date: purchaseOrder.po_date,
				vendor_id: purchaseOrder.vendor_id,
				vendor_name: purchaseOrder.vendor?.name || '',
				account: purchaseOrder.account,
				original_amount: purchaseOrder.original_amount,
				co_amount: purchaseOrder.co_amount,
				freight: purchaseOrder.freight,
				tax: purchaseOrder.tax,
				other: purchaseOrder.other,
				notes: purchaseOrder.notes,
			},
		};
	},
	default: async ({ request, locals, cookies }) => {
		await requireUser();
		const { supabase } = locals;
		const { org_name, client_name, project_name } = requireProject();

		const form = await superValidate(request, zod(workPackageSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Get project_id
		const { data: projectData, error: projectError } = await supabase
			.from('project')
			.select('*, client!inner(name, organization(name, org_id))')
			.eq('client.organization.name', org_name)
			.eq('client.name', client_name)
			.eq('name', project_name)
			.limit(1)
			.maybeSingle();

		if (projectError || !projectData) {
			console.error('Error fetching project:', projectError);
			return fail(404, { form, message: { type: 'error', text: 'Project not found' } });
		}

		// Create the work package
		const { data: workPackage, error: workPackageError } = await supabase
			.from('work_package')
			.insert({
				...form.data,
				project_id: projectData.project_id,
			})
			.select('work_package_id, name')
			.single();

		if (workPackageError) {
			console.error('Error creating work package:', workPackageError);
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to create work package' },
			});
		}

		throw redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(
				client_name,
			)}/projects/${encodeURIComponent(project_name)}/work-package`,
			{
				type: 'success',
				message: `Work package "${workPackage.name}" created successfully`,
			},
			cookies,
		);
	},
};
