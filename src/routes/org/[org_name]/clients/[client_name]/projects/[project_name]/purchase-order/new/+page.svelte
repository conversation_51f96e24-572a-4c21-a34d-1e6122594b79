<script lang="ts">
	import type { PageData } from './$types';
	import NewPurchaseOrder from '$lib/components/forms/purchase_order/NewPurchaseOrder.svelte';

	export const snapshot = {
		capture: () => ({
			formData: $formData,
			selectedVendor,
			vendorList,
		}),
		restore: (value) => {
			if (value) {
				Object.assign($formData, value.formData);
				selectedVendor = value.selectedVendor;
				vendorList = value.vendorList;
			}
		},
	};

	const { data }: { data: PageData } = $props();
	const { vendors, project } = data;

	const form = superForm(data.form, {
		validators: zodClient(purchaseOrderSchema),
		onUpdated({ form }) {
			console.log('in onUpdated: ', form);
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});

	const { form: formData, enhance, validate } = form;

	let vendorList = $state<VendorListItem[]>([...vendors] as VendorListItem[]);
	let dialogOpen = $state(false);

	// Date handling
	const df = new DateFormatter('default', {
		dateStyle: 'long',
	});

	let selectedVendor = $state($formData.vendor_id);
	let calendarOpen = $state(false);

	// Update form data when vendor selection changes
	$effect(() => {
		$formData.vendor_id = selectedVendor;
	});

	let poDateValue = $derived($formData.po_date ? parseDate($formData.po_date) : undefined);

	let placeholder = $state<DateValue>(today(getLocalTimeZone()));
	// Handle vendor creation from modal
	function handleVendorCreated(vendor: VendorListItem | null) {
		if (vendor) {
			// Convert VendorListItem to match the existing vendor list format
			const vendorForList = {
				vendor_id: vendor.vendor_id,
				name: vendor.name,
				description: vendor.description || '',
				vendor_type: vendor.vendor_type || '',
				contact_name: vendor.contact_name || '',
				contact_email: vendor.contact_email || '',
				contact_phone: vendor.contact_phone || '',
				is_active: vendor.is_active,
				access_level: vendor.access_level,
			};
			// Add the new vendor to the list (cast to match existing vendor list type)
			vendorList = [...vendorList, vendorForList as (typeof vendorList)[0]];
			// Auto-select the new vendor
			selectedVendor = vendor.vendor_id;
			// Manually update the form data to trigger validation
			$formData.vendor_id = vendor.vendor_id;
			// Trigger validation for the vendor_id field
			validate('vendor_id');
			// Close the dialog
			dialogOpen = false;
		} else {
			// Just close the dialog (cancel was clicked)
			dialogOpen = false;
		}
	}
</script>

<div class="container mx-auto py-8">
	<div class="mb-6">
		<h1 class="text-2xl font-semibold">Create Purchase Order</h1>
		<p class="text-muted-foreground mt-1">
			Create a new purchase order for {project.name}
		</p>
	</div>

	<div class="max-w-2xl">
		<form method="POST" use:enhance action="?/createPurchaseOrder">
			<div class="space-y-6">
				<!-- Basic Information -->
				<div class="space-y-4">
					<h2 class="text-lg font-medium">Basic Information</h2>

					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						<Form.Field {form} name="po_number">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>PO Number <span class="text-red-500">*</span></Form.Label>
									<Input {...props} bind:value={$formData.po_number} />
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="po_date">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>PO Date <span class="text-red-500">*</span></Form.Label>
									<Popover.Root bind:open={calendarOpen}>
										<Popover.Trigger>
											{#snippet child({ props: triggerProps })}
												<Button
													{...props}
													{...triggerProps}
													variant="outline"
													class="w-full justify-start text-left font-normal"
												>
													<CalendarIcon class="mr-2 h-4 w-4" />
													{poDateValue
														? df.format(poDateValue.toDate(getLocalTimeZone()))
														: 'Pick a date'}
												</Button>
											{/snippet}
										</Popover.Trigger>
										<Popover.Content class="w-auto p-0" align="start">
											<Calendar
												type="single"
												value={poDateValue as DateValue}
												bind:placeholder
												captionLayout="dropdown"
												onValueChange={(v) => {
													if (v) {
														$formData.po_date = v.toString();
													} else {
														$formData.po_date = '';
													}
													calendarOpen = false;
												}}
											/>
										</Popover.Content>
									</Popover.Root>
									<input type="hidden" name="po_date" bind:value={$formData.po_date} />
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
					</div>

					<Form.Field {form} name="vendor_id">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label class="flex-1">Vendor <span class="text-red-500">*</span></Form.Label>

								<div class="flex items-center gap-4">
									<Select.Root type="single" bind:value={selectedVendor} name={props.name}>
										<Select.Trigger {...props}>
											{selectedVendor
												? vendorList.find((v) => v.vendor_id === selectedVendor)?.name
												: 'Select vendor'}
										</Select.Trigger>
										<Select.Content>
											{#each vendorList as vendor (vendor.vendor_id)}
												<Select.Item value={vendor.vendor_id}>
													{vendor.name}
												</Select.Item>
											{/each}
										</Select.Content>
									</Select.Root>

									<p class="shrink-0 text-center">or</p>

									<Dialog.Root bind:open={dialogOpen}>
										<Dialog.Trigger class={cn(buttonVariants({ variant: 'secondary', size: 'sm' }))}
											>Create New Vendor</Dialog.Trigger
										>
										<Dialog.Content class="max-h-[90vh] overflow-auto">
											<NewVendor
												isModal={true}
												onVendorCreated={handleVendorCreated}
												data={{
													form: data.newVendorForm,
													organizations: [],
													clients: [
														{
															client: project.client,
															role: 'editor',
														},
													],
													projects: [
														{
															project: {
																project_id: project.project_id,
																name: project.name,
																client: project.client,
															},
															role: 'editor',
														},
													],
												}}
											/>
										</Dialog.Content>
									</Dialog.Root>
								</div>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>

					<Form.Field {form} name="description">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Description</Form.Label>
								<Textarea {...props} bind:value={$formData.description} />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>

					<Form.Field {form} name="account">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Account</Form.Label>
								<Input {...props} bind:value={$formData.account} />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<!-- Financial Information -->
				<div class="space-y-4">
					<h2 class="text-lg font-medium">Financial Information</h2>

					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						<Form.Field {form} name="original_amount">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Original Amount</Form.Label>
									<Input
										{...props}
										type="number"
										step="0.01"
										bind:value={$formData.original_amount}
									/>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="co_amount">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Change Order Amount</Form.Label>
									<Input {...props} type="number" step="0.01" bind:value={$formData.co_amount} />
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="freight">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Freight</Form.Label>
									<Input {...props} type="number" step="0.01" bind:value={$formData.freight} />
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="tax">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Tax</Form.Label>
									<Input {...props} type="number" step="0.01" bind:value={$formData.tax} />
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="other">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Other</Form.Label>
									<Input {...props} type="number" step="0.01" bind:value={$formData.other} />
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
					</div>
				</div>

				<!-- Notes -->
				<Form.Field {form} name="notes">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Notes</Form.Label>
							<Textarea {...props} bind:value={$formData.notes} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<!-- Actions -->
				<div class="flex gap-4">
					<Button type="submit">Create Purchase Order</Button>
					<!-- <Button type="button" variant="outline" onclick={() => history.back()}>Cancel</Button> -->
				</div>
			</div>
		</form>
	</div>
</div>
