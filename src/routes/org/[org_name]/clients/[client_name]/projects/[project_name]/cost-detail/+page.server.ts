import type { PageServerLoad } from './$types';
import type { Database } from '$lib/database.types';
import { requireUser, requireProject } from '$lib/server/auth';
import { error } from '@sveltejs/kit';

// Type definition for cost detail data from RPC function
export type CostDetailItem =
	Database['public']['Functions']['get_cost_detail_data']['Returns'][0] & {
		work_packages: Array<{
			work_package_id: string;
			name: string;
			description: string | null;
			purchase_order_id: string | null;
			purchase_order_number: string | null;
			vendor_name: string | null;
		}>;
		purchase_orders: Array<{
			purchase_order_id: string;
			po_number: string;
			description: string | null;
			vendor_name: string | null;
			original_amount: number | null;
			co_amount: number | null;
		}>;
	};

export const load: PageServerLoad = async ({ locals }) => {
	await requireUser();
	const { supabase } = locals;
	const { org_name, client_name, project_name } = requireProject();

	// Get project information
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select('*, client!inner(client_id, name, organization(name, org_id))')
		.eq('client.organization.name', org_name)
		.eq('client.name', client_name)
		.eq('name', project_name)
		.limit(1)
		.maybeSingle();

	if (projectError || !projectData) {
		console.error('Error fetching project:', projectError);
		throw error(404, 'Project not found');
	}

	// Fetch cost detail data using the RPC function
	const { data: costDetailData, error: costDetailError } = await supabase.rpc(
		'get_cost_detail_data',
		{
			project_id_param: projectData.project_id,
		},
	);

	if (costDetailError) {
		console.error('Error fetching cost detail data:', costDetailError);
		throw error(500, 'Failed to fetch cost detail data');
	}

	// Fetch all WBS items for hierarchy creation
	const { data: allWbsItems, error: wbsError } = await supabase
		.from('wbs_library_item')
		.select('*')
		.eq('wbs_library_id', projectData.wbs_library_id)
		.or(
			`client_id.eq.${projectData.client.client_id},project_id.eq.${projectData.project_id},item_type.eq.Standard`,
		);

	if (wbsError) {
		console.error('Error fetching WBS items:', wbsError);
		throw error(500, { message: 'Error loading WBS library data' });
	}

	return {
		project: projectData,
		costDetailData: costDetailData || [],
		allWbsItems: allWbsItems || [],
	};
};
